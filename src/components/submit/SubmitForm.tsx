'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { EntityType, Category, Tag, Feature, CreateEntityDto } from '@/types/entity';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import MultiSelectCheckbox from './MultiSelectCheckbox';
import ToolDetailsForm from './ToolDetailsForm';
import CourseDetailsForm from './CourseDetailsForm';
import AgencyDetailsForm from './AgencyDetailsForm';
import NewsletterDetailsForm from './NewsletterDetailsForm';
import SoftwareDetailsForm from './SoftwareDetailsForm';
import ModelDetailsForm from './ModelDetailsForm';
import DatasetDetailsForm from './DatasetDetailsForm';
import PlatformDetailsForm from './PlatformDetailsForm';
import HardwareDetailsForm from './HardwareDetailsForm';
import ResearchPaperDetailsForm from './ResearchPaperDetailsForm';
import JobDetailsForm from './JobDetailsForm';
import EventDetailsForm from './EventDetailsForm';
import PodcastDetailsForm from './PodcastDetailsForm';
import CommunityDetailsForm from './CommunityDetailsForm';
import GrantDetailsForm from './GrantDetailsForm';
import DefaultDetailsForm from './DefaultDetailsForm';

// Zod schema for form validation
const submitFormSchema = z.object({
  // Core entity information
  name: z.string().min(1, 'Name is required').max(200, 'Name must be less than 200 characters'),
  short_description: z.string().optional(),
  description: z.string().min(10, 'Description must be at least 10 characters').max(5000, 'Description must be less than 5000 characters'),
  website_url: z.string().url('Please enter a valid URL'),
  logo_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  documentation_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  contact_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  privacy_policy_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  founded_year: z.number().min(1900).max(new Date().getFullYear()).optional(),

  // Entity type and categorization
  entity_type_id: z.string().min(1, 'Please select an entity type'),
  category_ids: z.array(z.string()).optional(),
  tag_ids: z.array(z.string()).optional(),
  feature_ids: z.array(z.string()).optional(),

  // Company/organization details
  employee_count_range: z.string().optional(),
  funding_stage: z.string().optional(),
  location_summary: z.string().optional(),

  // Referral and affiliate
  ref_link: z.string().url('Please enter a valid URL').optional().or(z.literal('')),

  // SEO metadata
  meta_title: z.string().max(60, 'Meta title must be less than 60 characters').optional(),
  meta_description: z.string().max(160, 'Meta description must be less than 160 characters').optional(),

  // Details object for type-specific fields
  details: z.object({
    // Tool details
    technical_level: z.string().optional(),
    learning_curve: z.string().optional(),
    pricing_model: z.string().optional(),
    price_range: z.string().optional(),
    has_free_tier: z.boolean().optional(),
    has_api: z.boolean().optional(),
    open_source: z.boolean().optional(),
    mobile_support: z.boolean().optional(),
    trial_available: z.boolean().optional(),
    demo_available: z.boolean().optional(),
    has_live_chat: z.boolean().optional(),
    api_access: z.boolean().optional(),
    pricing_url: z.string().optional(),
    support_email: z.string().optional(),
    community_url: z.string().optional(),
    customization_level: z.string().optional(),
    current_version: z.string().optional(),
    pricing_details: z.string().optional(),
    key_features_text: z.string().optional(),
    use_cases_text: z.string().optional(),
    target_audience_text: z.string().optional(),
    support_channels_text: z.string().optional(),
    frameworks_text: z.string().optional(),
    integrations_text: z.string().optional(),
    libraries_text: z.string().optional(),
    platforms_text: z.string().optional(),
    programming_languages_text: z.string().optional(),
    supported_os_text: z.string().optional(),
    deployment_options_text: z.string().optional(),

    // Course details
    instructor_name: z.string().optional(),
    duration_text: z.string().optional(),
    skill_level: z.string().optional(),
    language: z.string().optional(),
    certificate_available: z.boolean().optional(),
    syllabus_url: z.string().optional(),
    enrollment_count: z.number().optional(),
    prerequisites_text: z.string().optional(),
    learning_outcomes_text: z.string().optional(),

    // Agency details
    portfolio_url: z.string().optional(),
    team_size: z.string().optional(),
    contact_email: z.string().optional(),
    region_served: z.string().optional(),
    location_summary: z.string().optional(),
    pricing_info: z.string().optional(),
    services_offered_text: z.string().optional(),
    specializations_text: z.string().optional(),
    industry_focus_text: z.string().optional(),
    target_client_size_text: z.string().optional(),

    // Newsletter details
    author_name: z.string().optional(),
    frequency: z.string().optional(),
    archive_url: z.string().optional(),
    subscribe_url: z.string().optional(),
    subscriber_count: z.number().optional(),
    target_audience: z.string().optional(),
    main_topics_text: z.string().optional(),

    // Software details (unique fields not in tool details)
    license_type: z.string().optional(),
    platform_compatibility_text: z.string().optional(),

    // Model details
    model_architecture: z.string().optional(),
    training_dataset: z.string().optional(),
    license: z.string().optional(),
    performance_metrics: z.string().optional(),
    input_data_types_text: z.string().optional(),
    output_data_types_text: z.string().optional(),

    // Dataset details
    size_in_bytes: z.number().optional(),
    source_url: z.string().optional(),
    collection_method: z.string().optional(),
    format: z.string().optional(),
    update_frequency: z.string().optional(),
    access_notes: z.string().optional(),

    // Platform details (unique fields not in other forms)
    platform_type: z.string().optional(),
    key_services_text: z.string().optional(),

    // Hardware details
    gpu: z.string().optional(),
    processor: z.string().optional(),
    memory: z.string().optional(),
    storage: z.string().optional(),
    power_consumption: z.string().optional(),
    price: z.string().optional(),
    availability: z.string().optional(),

    // Research paper details
    doi: z.string().optional(),
    journal_or_conference: z.string().optional(),
    publication_date: z.string().optional(),
    citation_count: z.number().optional(),
    pdf_url: z.string().optional(),
    abstract: z.string().optional(),
    authors_text: z.string().optional(),

    // Job details
    company_name: z.string().optional(),
    job_type: z.string().optional(),
    experience_level: z.string().optional(),
    location: z.string().optional(),
    salary_min: z.number().optional(),
    salary_max: z.number().optional(),
    application_url: z.string().optional(),
    is_remote: z.boolean().optional(),
    key_responsibilities_text: z.string().optional(),
    required_skills_text: z.string().optional(),

    // Event details
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    event_type: z.string().optional(),
    is_online: z.boolean().optional(),
    registration_url: z.string().optional(),
    key_speakers_text: z.string().optional(),
    topics_text: z.string().optional(),

    // Podcast details
    host: z.string().optional(),
    average_length: z.string().optional(),
    apple_podcasts_url: z.string().optional(),
    spotify_url: z.string().optional(),
    google_podcasts_url: z.string().optional(),
    youtube_url: z.string().optional(),

    // Community details
    platform: z.string().optional(),
    member_count: z.number().optional(),
    invite_url: z.string().optional(),
    main_channel_url: z.string().optional(),
    rules_url: z.string().optional(),
    focus_topics_text: z.string().optional(),

    // Grant details
    funder_name: z.string().optional(),
    grant_type: z.string().optional(),
    amount: z.string().optional(),
    deadline: z.string().optional(),
    eligibility: z.string().optional(),
    focus_areas_text: z.string().optional(),

    // Default details
    additional_details: z.string().optional(),
  }).optional(),
});

type SubmitFormData = z.infer<typeof submitFormSchema>;

interface SubmitFormProps {
  entityTypes: EntityType[];
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  onSubmit: (data: CreateEntityDto) => Promise<void>;
  isSubmitting: boolean;
}

export default function SubmitForm({
  entityTypes,
  categories,
  tags,
  features,
  onSubmit,
  isSubmitting
}: SubmitFormProps) {
  const [selectedEntityTypeId, setSelectedEntityTypeId] = useState<string>('');
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [selectedFeatureIds, setSelectedFeatureIds] = useState<string[]>([]);

  const form = useForm<SubmitFormData>({
    resolver: zodResolver(submitFormSchema),
    defaultValues: {
      name: '',
      short_description: '',
      description: '',
      website_url: '',
      logo_url: '',
      documentation_url: '',
      contact_url: '',
      privacy_policy_url: '',
      entity_type_id: '',
      category_ids: [],
      tag_ids: [],
      feature_ids: [],
      ref_link: '',
      meta_title: '',
      meta_description: '',
    }
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch } = form;

  const handleEntityTypeChange = (value: string) => {
    setSelectedEntityTypeId(value);
    setValue('entity_type_id', value);
  };

  // Helper function to convert comma-separated strings to arrays
  const stringToArray = (str: string | undefined): string[] | undefined => {
    if (!str || str.trim() === '') return undefined;
    return str.split(',').map(item => item.trim()).filter(item => item.length > 0);
  };

  const handleFormSubmit: SubmitHandler<SubmitFormData> = async (data) => {
    try {
      // Get the selected entity type to determine the correct details key
      const selectedEntityType = entityTypes.find(type => type.id === selectedEntityTypeId);

      // Process details object with array conversions
      const processedDetails = data.details ? {
        ...data.details,
        // Convert text fields to arrays for tool details
        key_features: stringToArray(data.details.key_features_text),
        use_cases: stringToArray(data.details.use_cases_text),
        target_audience: stringToArray(data.details.target_audience_text),
        support_channels: stringToArray(data.details.support_channels_text),
        frameworks: stringToArray(data.details.frameworks_text),
        integrations: stringToArray(data.details.integrations_text),
        libraries: stringToArray(data.details.libraries_text),
        platforms: stringToArray(data.details.platforms_text),
        programming_languages: stringToArray(data.details.programming_languages_text),
        supported_os: stringToArray(data.details.supported_os_text),
        deployment_options: stringToArray(data.details.deployment_options_text),
        platform_compatibility: stringToArray(data.details.platform_compatibility_text),
        // Convert text fields to arrays for model details
        input_data_types: stringToArray(data.details.input_data_types_text),
        output_data_types: stringToArray(data.details.output_data_types_text),
        // Convert text fields to arrays for platform details
        key_services: stringToArray(data.details.key_services_text),
        // Convert text fields to arrays for new entity types
        authors: stringToArray(data.details.authors_text),
        key_responsibilities: stringToArray(data.details.key_responsibilities_text),
        required_skills: stringToArray(data.details.required_skills_text),
        key_speakers: stringToArray(data.details.key_speakers_text),
        topics: stringToArray(data.details.topics_text),
        focus_topics: stringToArray(data.details.focus_topics_text),
        focus_areas: stringToArray(data.details.focus_areas_text),
        // Convert text fields to arrays for course details
        prerequisites: stringToArray(data.details.prerequisites_text),
        learning_outcomes: stringToArray(data.details.learning_outcomes_text),
        // Convert text fields to arrays for agency details
        services_offered: stringToArray(data.details.services_offered_text),
        specializations: stringToArray(data.details.specializations_text),
        industry_focus: stringToArray(data.details.industry_focus_text),
        target_client_size: stringToArray(data.details.target_client_size_text),
        // Convert text fields to arrays for newsletter details
        main_topics: stringToArray(data.details.main_topics_text),
        // Remove the text versions
        key_features_text: undefined,
        use_cases_text: undefined,
        target_audience_text: undefined,
        support_channels_text: undefined,
        frameworks_text: undefined,
        integrations_text: undefined,
        libraries_text: undefined,
        platforms_text: undefined,
        programming_languages_text: undefined,
        supported_os_text: undefined,
        deployment_options_text: undefined,
        platform_compatibility_text: undefined,
        input_data_types_text: undefined,
        output_data_types_text: undefined,
        key_services_text: undefined,
        authors_text: undefined,
        key_responsibilities_text: undefined,
        required_skills_text: undefined,
        key_speakers_text: undefined,
        topics_text: undefined,
        focus_topics_text: undefined,
        focus_areas_text: undefined,
        prerequisites_text: undefined,
        learning_outcomes_text: undefined,
        services_offered_text: undefined,
        specializations_text: undefined,
        industry_focus_text: undefined,
        target_client_size_text: undefined,
        main_topics_text: undefined,
      } : undefined;

      // Create the base payload without details
      const basePayload: any = {
        name: data.name,
        short_description: data.short_description || undefined,
        description: data.description,
        website_url: data.website_url,
        logo_url: data.logo_url || undefined,
        documentation_url: data.documentation_url || undefined,
        contact_url: data.contact_url || undefined,
        privacy_policy_url: data.privacy_policy_url || undefined,
        founded_year: data.founded_year || undefined,
        entity_type_id: data.entity_type_id,
        category_ids: selectedCategoryIds.length > 0 ? selectedCategoryIds : undefined,
        tag_ids: selectedTagIds.length > 0 ? selectedTagIds : undefined,
        feature_ids: selectedFeatureIds.length > 0 ? selectedFeatureIds : undefined,
        employee_count_range: data.employee_count_range || undefined,
        funding_stage: data.funding_stage || undefined,
        location_summary: data.location_summary || undefined,
        ref_link: data.ref_link || undefined,
        meta_title: data.meta_title || undefined,
        meta_description: data.meta_description || undefined,
      };

      // Add the type-specific details with the correct key and filter out invalid fields
      if (selectedEntityType && processedDetails) {
        switch (selectedEntityType.slug) {
          case 'ai-tool':
          case 'tool':
            // Start with minimal fields and add based on API testing
            basePayload.tool_details = {
              // Only include fields that we know work - start minimal
              pricing_details: processedDetails.pricing_details || undefined,
              key_features: processedDetails.key_features || undefined,
              use_cases: processedDetails.use_cases || undefined,
              // Test these fields individually
              // technical_level: processedDetails.technical_level || undefined,
              // learning_curve: processedDetails.learning_curve || undefined,
              // has_api: processedDetails.has_api || false,
              // has_free_tier: processedDetails.has_free_tier || false,
              // pricing_model: processedDetails.pricing_model || undefined,
              // price_range: processedDetails.price_range || undefined,
            };
            // Remove undefined values to avoid validation errors
            Object.keys(basePayload.tool_details).forEach(key => {
              if (basePayload.tool_details[key] === undefined) {
                delete basePayload.tool_details[key];
              }
            });
            break;
          case 'course':
            basePayload.course_details = processedDetails;
            break;
          case 'agency':
            basePayload.agency_details = processedDetails;
            break;
          case 'newsletter':
            basePayload.newsletter_details = processedDetails;
            break;
          case 'software':
            basePayload.software_details = processedDetails;
            break;
          case 'model':
            basePayload.model_details = processedDetails;
            break;
          case 'dataset':
            basePayload.dataset_details = processedDetails;
            break;
          case 'platform':
            basePayload.platform_details = processedDetails;
            break;
          case 'hardware':
            basePayload.hardware_details = processedDetails;
            break;
          case 'research-paper':
          case 'paper':
            basePayload.research_paper_details = processedDetails;
            break;
          case 'job':
            basePayload.job_details = processedDetails;
            break;
          case 'event':
            basePayload.event_details = processedDetails;
            break;
          case 'podcast':
            basePayload.podcast_details = processedDetails;
            break;
          case 'community':
            basePayload.community_details = processedDetails;
            break;
          case 'grant':
            basePayload.grant_details = processedDetails;
            break;
          case 'content-creator':
            basePayload.content_creator_details = processedDetails;
            break;
          default:
            // For unknown types, we can still include the details
            basePayload.details = processedDetails;
            break;
        }
      }

      await onSubmit(basePayload);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const selectedEntityType = entityTypes.find(type => type.id === selectedEntityTypeId);

  const renderTypeSpecificDetails = () => {
    if (!selectedEntityType) return null;

    const commonProps = {
      register,
      errors,
      setValue,
      watch
    };

    switch (selectedEntityType.slug) {
      case 'ai-tool':
      case 'tool':
        return <ToolDetailsForm {...commonProps} />;
      case 'course':
        return <CourseDetailsForm {...commonProps} />;
      case 'agency':
        return <AgencyDetailsForm {...commonProps} />;
      case 'newsletter':
        return <NewsletterDetailsForm {...commonProps} />;
      case 'software':
        return <SoftwareDetailsForm {...commonProps} />;
      case 'model':
        return <ModelDetailsForm {...commonProps} />;
      case 'dataset':
        return <DatasetDetailsForm {...commonProps} />;
      case 'platform':
        return <PlatformDetailsForm {...commonProps} />;
      case 'hardware':
        return <HardwareDetailsForm {...commonProps} />;
      case 'research-paper':
      case 'paper':
        return <ResearchPaperDetailsForm {...commonProps} />;
      case 'job':
        return <JobDetailsForm {...commonProps} />;
      case 'event':
        return <EventDetailsForm {...commonProps} />;
      case 'podcast':
        return <PodcastDetailsForm {...commonProps} />;
      case 'community':
        return <CommunityDetailsForm {...commonProps} />;
      case 'grant':
        return <GrantDetailsForm {...commonProps} />;
      case 'content-creator':
      default:
        return <DefaultDetailsForm {...commonProps} entityTypeName={selectedEntityType.name} />;
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
      {/* Step 1: Entity Type Selection */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="entityType" className="text-base font-semibold">
            What kind of resource are you submitting? *
          </Label>
          <Select onValueChange={handleEntityTypeChange} value={selectedEntityTypeId}>
            <SelectTrigger className="mt-2">
              <SelectValue placeholder="Select a resource type..." />
            </SelectTrigger>
            <SelectContent>
              {entityTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.entity_type_id && (
            <p className="mt-1 text-sm text-red-600">{errors.entity_type_id.message}</p>
          )}
        </div>

        {selectedEntityType && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900">{selectedEntityType.name}</h3>
            <p className="text-sm text-blue-700 mt-1">{selectedEntityType.description}</p>
          </div>
        )}
      </div>

      {/* Step 2: Core Information (shown after entity type is selected) */}
      {selectedEntityTypeId && (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Core Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label htmlFor="name">Resource Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter the name of the resource"
                  className="mt-1"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="website_url">Website URL *</Label>
                <Input
                  id="website_url"
                  type="url"
                  {...register('website_url')}
                  placeholder="https://example.com"
                  className="mt-1"
                />
                {errors.website_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.website_url.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="short_description">Short Description</Label>
                <Input
                  id="short_description"
                  {...register('short_description')}
                  placeholder="Brief one-line description (optional)"
                  className="mt-1"
                />
                {errors.short_description && (
                  <p className="mt-1 text-sm text-red-600">{errors.short_description.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="description">Full Description *</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Provide a detailed description of the resource, its features, and benefits..."
                  className="mt-1 min-h-[120px]"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="logo_url">Logo URL</Label>
                <Input
                  id="logo_url"
                  type="url"
                  {...register('logo_url')}
                  placeholder="https://example.com/logo.png (optional)"
                  className="mt-1"
                />
                {errors.logo_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.logo_url.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="documentation_url">Documentation URL</Label>
                <Input
                  id="documentation_url"
                  type="url"
                  {...register('documentation_url')}
                  placeholder="https://docs.example.com (optional)"
                  className="mt-1"
                />
                {errors.documentation_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.documentation_url.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Categorization Section */}
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Categorization</h2>

            <div className="space-y-6">
              <MultiSelectCheckbox
                label="Categories"
                options={categories.map(cat => ({
                  id: cat.id,
                  name: cat.name,
                  description: cat.description
                }))}
                selectedIds={selectedCategoryIds}
                onSelectionChange={setSelectedCategoryIds}
                showDescription={true}
                columns={2}
              />

              <MultiSelectCheckbox
                label="Tags"
                options={tags.map(tag => ({
                  id: tag.id,
                  name: tag.name
                }))}
                selectedIds={selectedTagIds}
                onSelectionChange={setSelectedTagIds}
                columns={3}
              />

              <MultiSelectCheckbox
                label="Features"
                options={features.map(feature => ({
                  id: feature.id,
                  name: feature.name,
                  description: feature.description
                }))}
                selectedIds={selectedFeatureIds}
                onSelectionChange={setSelectedFeatureIds}
                showDescription={true}
                columns={2}
              />
            </div>
          </div>

          {/* Additional Fields Section */}
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="contact_url">Contact URL</Label>
                <Input
                  id="contact_url"
                  type="url"
                  {...register('contact_url')}
                  placeholder="https://example.com/contact (optional)"
                  className="mt-1"
                />
                {errors.contact_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.contact_url.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="privacy_policy_url">Privacy Policy URL</Label>
                <Input
                  id="privacy_policy_url"
                  type="url"
                  {...register('privacy_policy_url')}
                  placeholder="https://example.com/privacy (optional)"
                  className="mt-1"
                />
                {errors.privacy_policy_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.privacy_policy_url.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="founded_year">Founded Year</Label>
                <Input
                  id="founded_year"
                  type="number"
                  {...register('founded_year', { valueAsNumber: true })}
                  placeholder="2023 (optional)"
                  className="mt-1"
                  min="1900"
                  max={new Date().getFullYear()}
                />
                {errors.founded_year && (
                  <p className="mt-1 text-sm text-red-600">{errors.founded_year.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="ref_link">Referral Link</Label>
                <Input
                  id="ref_link"
                  type="url"
                  {...register('ref_link')}
                  placeholder="https://example.com/ref/123 (optional)"
                  className="mt-1"
                />
                {errors.ref_link && (
                  <p className="mt-1 text-sm text-red-600">{errors.ref_link.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Type-Specific Details Section */}
          {selectedEntityType && (
            <div className="border-t pt-6">
              {renderTypeSpecificDetails()}
            </div>
          )}

          {/* Submit button */}
          <div className="border-t pt-6">
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full md:w-auto"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Resource'}
            </Button>
          </div>
        </div>
      )}
    </form>
  );
}
